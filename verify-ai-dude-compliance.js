const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Supabase configuration
const supabaseUrl = 'https://gvcdqspryxrvxadfpwux.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd2Y2Rxc3ByeXhydnhhZGZwd3V4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTA0NDE0NiwiZXhwIjoyMDY0NjIwMTQ2fQ.s1w4ohjetf2iHfhpnvrBRZ0L5ORu53Oovpr6vyCPbhw';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Expected templates as per implementation strategy
const expectedTemplates = [
  {
    config_key: 'prompt_ai_dude_complete_system',
    config_value: {
      name: "AI Dude Complete Content Generation System",
      description: "System prompt for complete tool content generation with ALL database fields",
      category: "content",
      promptType: "system",
      template: `You are "AI Dude," the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

{DATABASE_SCHEMA}

**Tone rules:**
- Always write like a snarky, witty "AI Dude."
- Keep it punchy: no corporate sugarcoating.
- Use contractions, slang, and street-smart humor.
- Never apologize or say "I'm sorry."
- Write "description" as a one-sentence hook.
- Write "short_description" as a punchy card summary (max 150 chars).
- Make "detailed_description" engaging and informative (150-300 words).
- Create "meta_title" and "meta_description" that are SEO-optimized but still snarky.
- Ensure "category_confidence" is 0.90+ if obvious; 0.80-0.75 if guessing.

**Field Requirements:**
- **Required fields**: name, description, short_description, detailed_description, company, category_primary, features, pricing, pros_and_cons, faqs, hashtags, meta_title, meta_description
- **Optional fields**: Fill when information is available in scraped content
- **FAQ generation**: Create 3-5 relevant Q&As in AI Dude style with complete metadata
- **SEO optimization**: meta_title (max 60 chars), meta_description (150-160 chars), meta_keywords (future implementation)
- **Excluded fields**: logo_url, website, screenshots, claim_info, generated_content (handled by other systems)

**VERY IMPORTANT:**
- Output exactly one JSON object.
- Do not wrap it in backticks or code fences.
- Do not add extra fields or comments.
- If any section is missing, use appropriate defaults: "" for strings, [] for arrays, {} for objects.
- Always format dates as YYYY-MM-DD.
- Generate UUIDs for FAQ entries.
- Include complete generated_content metadata.

Now read the user content and produce the complete JSON with ALL required fields.`,
      variables: ["DATABASE_SCHEMA"],
      validationRules: ["All required fields present", "Field length limits", "SEO optimization", "Complete FAQ structure"],
      formatRequirements: "Complete JSON output with all database fields",
      usage: 0
    },
    config_type: 'prompt_template',
    description: 'AI Dude complete content generation system prompt'
  },
  {
    config_key: 'prompt_ai_dude_partial_context',
    config_value: {
      name: "AI Dude Partial Generation with Context",
      description: "User prompt for partial content generation with existing tool data context",
      category: "partial",
      promptType: "user",
      template: `You are "AI Dude," the irreverent, no-BS curator of AI tools. Generate ONLY the {sectionType} section for this tool in your signature snarky style.

**Existing Tool Data (for context):**
{existingToolData}

**New Scraped Content:**
{scrapedContent}

**Tool URL:** {toolUrl}

**Section to Generate:** {sectionType}

**Section Requirements:**
{sectionRequirements}

**Instructions:**
- Use the existing tool data for context and consistency
- Focus ONLY on generating the {sectionType} section
- Maintain consistency with existing tone and style
- If updating existing content, improve and enhance it
- Keep the irreverent, witty "AI Dude" voice throughout
- For FAQs: Include complete metadata structure with UUIDs
- For SEO: Optimize for search while maintaining personality

Output only the requested section in JSON format matching the database schema.`,
      variables: ["sectionType", "existingToolData", "scrapedContent", "toolUrl", "sectionRequirements"],
      validationRules: ["Section-specific validation", "Consistency with existing data", "Complete field structure"],
      formatRequirements: "JSON object containing only the requested section with proper schema structure",
      usage: 0
    },
    config_type: 'prompt_template',
    description: 'AI Dude partial generation with context'
  }
];

async function verifyCompliance() {
  console.log('🔍 AI Dude Prompt System Compliance Verification\n');
  console.log('='.repeat(60));

  try {
    // Step 1: Remove existing AI Dude templates
    console.log('\n1. 🧹 Cleaning existing AI Dude templates...');
    const { error: deleteError } = await supabase
      .from('system_configuration')
      .delete()
      .like('config_key', 'prompt_ai_dude%');

    if (deleteError) {
      console.log(`   ⚠️  Delete warning: ${deleteError.message}`);
    } else {
      console.log('   ✅ Existing templates cleaned');
    }

    // Step 2: Install compliant templates
    console.log('\n2. 📝 Installing implementation strategy compliant templates...');
    
    for (let i = 0; i < expectedTemplates.length; i++) {
      const template = expectedTemplates[i];
      console.log(`   Installing: ${template.config_value.name}`);
      
      const { data, error } = await supabase
        .from('system_configuration')
        .insert(template);

      if (error) {
        console.log(`   ❌ Error: ${error.message}`);
      } else {
        console.log(`   ✅ Success`);
      }
    }

    // Step 3: Verify installation
    console.log('\n3. 🔍 Verifying installation compliance...');
    const { data: installedTemplates, error: verifyError } = await supabase
      .from('system_configuration')
      .select('*')
      .like('config_key', 'prompt_ai_dude%')
      .order('config_key');

    if (verifyError) {
      console.log(`   ❌ Verification error: ${verifyError.message}`);
      return;
    }

    console.log(`   ✅ Found ${installedTemplates.length} templates (expected: 2)`);

    // Step 4: Detailed compliance check
    console.log('\n4. 📋 Detailed Compliance Analysis...');
    
    let complianceScore = 0;
    const maxScore = 10;

    // Check template count
    if (installedTemplates.length === 2) {
      console.log('   ✅ Template count: 2/2 (correct)');
      complianceScore += 2;
    } else {
      console.log(`   ❌ Template count: ${installedTemplates.length}/2 (incorrect)`);
    }

    // Check each template
    for (const expected of expectedTemplates) {
      const installed = installedTemplates.find(t => t.config_key === expected.config_key);
      
      if (!installed) {
        console.log(`   ❌ Missing template: ${expected.config_key}`);
        continue;
      }

      console.log(`\n   📄 Checking: ${expected.config_key}`);
      
      // Check template content
      const expectedTemplate = expected.config_value.template;
      const installedTemplate = installed.config_value.template;
      
      // Key compliance checks
      const checks = [
        {
          name: 'AI Dude persona with quotes',
          test: installedTemplate.includes('"AI Dude,"'),
          points: 1
        },
        {
          name: 'Correct apostrophe in "I\'m sorry"',
          test: installedTemplate.includes('"I\'m sorry."'),
          points: 1
        },
        {
          name: 'Specific field instructions',
          test: installedTemplate.includes('"description" as a one-sentence hook') &&
                installedTemplate.includes('"short_description" as a punchy card summary') &&
                installedTemplate.includes('"detailed_description" engaging and informative'),
          points: 2
        },
        {
          name: 'Category confidence specification',
          test: installedTemplate.includes('0.90+ if obvious; 0.80-0.75 if guessing'),
          points: 1
        },
        {
          name: 'Required fields list',
          test: installedTemplate.includes('name, description, short_description, detailed_description, company, category_primary, features, pricing, pros_and_cons, faqs, hashtags, meta_title, meta_description'),
          points: 1
        }
      ];

      for (const check of checks) {
        if (check.test) {
          console.log(`      ✅ ${check.name}`);
          complianceScore += check.points;
        } else {
          console.log(`      ❌ ${check.name}`);
        }
      }
    }

    // Final compliance report
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPLIANCE REPORT');
    console.log('='.repeat(60));
    
    const compliancePercentage = Math.round((complianceScore / maxScore) * 100);
    console.log(`Compliance Score: ${complianceScore}/${maxScore} (${compliancePercentage}%)`);
    
    if (compliancePercentage >= 90) {
      console.log('✅ STATUS: FULLY COMPLIANT');
      console.log('🎯 AI Dude templates match implementation strategy specifications');
    } else if (compliancePercentage >= 70) {
      console.log('⚠️  STATUS: MOSTLY COMPLIANT');
      console.log('🔧 Minor adjustments needed to match specifications');
    } else {
      console.log('❌ STATUS: NON-COMPLIANT');
      console.log('🚨 Significant changes needed to match specifications');
    }

    console.log('\n📋 Templates installed:');
    installedTemplates.forEach(template => {
      console.log(`   - ${template.config_key}: ${template.config_value.name} (${template.config_value.promptType})`);
    });

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

verifyCompliance();
